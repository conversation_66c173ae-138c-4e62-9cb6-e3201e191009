# standard library
import os
from pydantic import BaseModel, <PERSON>
from typing import Optional, Union

# Third-Party Libraries
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_403_FORBIDDEN
from fastapi import (
    FastAPI,
    HTTPException,
    Request,
)
import bittensor as bt
from dotenv import load_dotenv

# Loads the .env file
load_dotenv()


class DeviceRequirement(BaseModel):
    cpu_count: int = Field(default=1, description="CPU count")
    gpu_type: str = Field(default="gpu", description="GPU Name")
    gpu_size: int = Field(default=3, description="GPU size in GB")
    ram: int = Field(default=1, description="RAM size in GB")
    hard_disk: int = Field(default=1, description="Hard disk size in GB")
    timeline: int = Field(
        default=90, description="Rent Timeline in day"
    )  # timeline=90 day by spec, 30 day by hotkey


class Allocation(BaseModel):
    resource: str = ""
    hotkey: str = ""
    regkey: str = ""
    ssh_ip: str = ""
    ssh_port: int = 4444
    external_port: int = 27015
    ssh_username: str = ""
    ssh_password: str = ""
    ssh_command: str = ""
    status: str = ""
    ssh_key: str = ""
    uuid_key: str = ""
    miner_version: int = 0


class DockerRequirement(BaseModel):
    base_image: str = "ubuntu"
    ssh_key: str = ""
    volume_path: str = "/tmp"
    dockerfile: str = ""


class ResourceGPU(BaseModel):
    gpu_name: str = ""
    gpu_capacity: int = 0
    gpu_count: int = 1


class Resource(BaseModel):
    hotkey: str = ""
    cpu_count: int = 1
    gpu_name: str = ""
    gpu_capacity: Union[str, float] = ""
    gpu_count: int = 1
    ip: str = ""
    geo: str = ""
    ram: Union[str, float] = ""
    hard_disk: Union[str, float] = "0"
    allocate_status: str = ""  # "Avail." or "Res."
    version: int = 0 # miner version


class Specs(BaseModel):
    details: str = ""


class ResourceQuery(BaseModel):
    gpu_name: Optional[str] = None
    cpu_count_min: Optional[int] = None
    cpu_count_max: Optional[int] = None
    gpu_capacity_min: Optional[float] = None
    gpu_capacity_max: Optional[float] = None
    hard_disk_total_min: Optional[float] = None
    hard_disk_total_max: Optional[float] = None
    ram_total_min: Optional[float] = None
    ram_total_max: Optional[float] = None


# IP Whitelist middleware
class IPWhitelistMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: FastAPI):
        super().__init__(app)
        self.whitelisted_ips = set(os.getenv("WHITELISTED_IPS", "").split(","))

    async def dispatch(self, request: Request, call_next):
        # Extracts the client's IP address
        client_ip = request.client.host
        if client_ip not in self.whitelisted_ips:
            bt.logging.info(f"Access attempt from IP: {client_ip}")
            raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Access forbidden: IP not whitelisted")

        # Process the request and get the response
        response = await call_next(request)
        return response
