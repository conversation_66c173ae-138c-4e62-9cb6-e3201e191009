# Standard Library Imports
import json
import os
import uuid
import asyncio
import hmac
import hashlib
import time
from datetime import datetime, timezone
from dotenv import load_dotenv
# Third-Party Imports
import requests
import urllib3
from bittensor import Dendrite, Metagraph
from compute.utils.db import ComputeDb
from compute.wandb.wandb import Compute<PERSON>andb
from urllib3.exceptions import InsecureRequestWarning

# Local Imports
import bittensor as bt

# Import Compute Subnet Libraries
import neurons.RSAEncryption as rsa
from compute.protocol import Allocate
from compute.pubsub import (
    PubSubClient,
    MessageFactory,
    create_allocation_ended_message,
    create_miner_offline_message,
    create_miner_online_message,
)
from neurons.Validator.database.allocate import (
    update_allocation_db,
    get_miner_details,
)

from libs.register.allocate import (
    allocate_best_candidate,
    process_allocation_result,
    process_result,
    check_and_allocate,
    get_hotkey_list,
)

from libs.register.deallocate import (
    get_deregister_response_status,
)

from api.models.register import (
    Allocation,
)

from api.constants import (
    MAX_NOTIFY_RETRY,
    MAX_ALLOCATION_RETRY,
    NOTIFY_RETRY_PERIOD,
    MINER_BLACKLIST,
)

from fastapi.responses import JSONResponse
from api.models.register import DeviceRequirement, DockerRequirement
from fastapi import (
    status,
    Request,
)
from fastapi.encoders import jsonable_encoder
from fastapi.concurrency import run_in_threadpool

load_dotenv()
urllib3.disable_warnings(InsecureRequestWarning)
secret = os.getenv("WEBHOOKS_SECRET", "the shared secret key").encode()


async def allocate_spec(
    db: ComputeDb,
    wandb: ComputeWandb,
    metagraph: Metagraph,
    dendrite: Dendrite,
    requirements: DeviceRequirement,
    docker_requirement: DockerRequirement,
) -> JSONResponse:
    """
    The GPU resource allocate API endpoint.
    requirements: The GPU resource requirements which contain the GPU type, GPU size, ram, hard_disk
    and booking timeline.
    """
    # client_host = request.client.host
    if requirements:
        device_requirement = {
            "cpu": {"count": requirements.cpu_count},
            "gpu": {},
            "hard_disk": {"capacity": requirements.hard_disk * 1024.0**3},
            "ram": {"capacity": requirements.ram * 1024.0**3},
        }
        if requirements.gpu_type != "" and int(requirements.gpu_size) != 0:
            device_requirement["gpu"] = {
                "count": 1,
                "capacity": int(requirements.gpu_size) * 1000,
                "type": requirements.gpu_type,
            }
        # Generate UUID
        uuid_key = str(uuid.uuid1())
        timeline = int(requirements.timeline)
        private_key, public_key = rsa.generate_key_pair()
        bt_components = {
            "dendrite": dendrite,
            "metagraph": metagraph,
        }
        result = await run_in_threadpool(
            allocate_best_candidate,
            db,
            bt_components,
            device_requirement,
            timeline,
            public_key,
            docker_requirement.model_dump(),
        )

        if result["status"] is False:
            bt.logging.info(f"API: Allocation Failed : {result['msg']}")
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Fail to allocate resource",
                    "err_detail": result["msg"],
                },
            )
        info = process_allocation_result(db, result, private_key, public_key)
        info["ssh_key"] = docker_requirement.ssh_key
        info["uuid"] = uuid_key
        await asyncio.sleep(1)
        allocated = Allocation()
        allocated.resource = info["resource"]
        # allocated.hotkey = result_hotkey
        allocated.hotkey = info["result_hotkey"]
        # allocated.regkey = info["regkey"]
        allocated.ssh_key = info["ssh_key"]
        allocated.ssh_ip = info["ip"]
        allocated.ssh_port = info["port"]
        allocated.external_port = info["fixed_external_user_port"]
        allocated.ssh_username = info["username"]
        allocated.ssh_password = info["password"]
        allocated.uuid_key = info["uuid"]
        allocated.ssh_command = (
            f"ssh {info['username']}@{result['ip']} -p {str(info['port'])}"
        )
        allocated.miner_version = result.get("miner_version",0)
        # update_allocation_db(result_hotkey, info, True)
        update_allocation_db(info["result_hotkey"], info, True)
        await _update_allocation_wandb(db, wandb)
        # bt.logging.info(f"API: Resource {result_hotkey} was successfully allocated")
        bt.logging.info(
            f"API: Resource {info['result_hotkey']} was successfully allocated"
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "message": "Resource was successfully allocated",
                "data": jsonable_encoder(allocated),
            },
        )
    else:
        bt.logging.error("API: Invalid allocation request")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "success": False,
                "message": "Invalid allocation request",
                "err_detail": "Invalid requirement, please check the requirements",
            },
        )


async def allocate_hotkey(
    db: ComputeDb,
    metagraph: Metagraph,
    dendrite: Dendrite,
    wandb: ComputeWandb,
    hotkey: str,
    ssh_key: str | None = None,
    docker_requirement: DockerRequirement | None = None,
) -> JSONResponse:
    """
    The GPU allocate by hotkey API endpoint.
    User use this API to book a specific miner.
    hotkey: The miner hotkey to allocate the gpu resource.
    """
    try:
        if hotkey in MINER_BLACKLIST:
            bt.logging.warning(f"Allocation request by blacklisted hotkey: {hotkey}")
            bt.logging.error(f"API: Allocation {hotkey} Failed : blacklisted")
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "Fail to allocate resource, blacklisted",
                    "err_detail": "blacklisted",
                },
            )

        if hotkey:
            # client_host = request.client.host
            requirements = DeviceRequirement()
            requirements.gpu_type = ""
            requirements.gpu_size = 0
            requirements.timeline = 30
            # Generate UUID
            uuid_key = str(uuid.uuid1())
            private_key, public_key = rsa.generate_key_pair()
            if docker_requirement is None:
                docker_requirement = DockerRequirement()
            if ssh_key is None:
                docker_requirement.ssh_key = ""
            else:
                docker_requirement.ssh_key = ssh_key
            result = await _allocate_container_hotkey(
                metagraph,
                dendrite,
                requirements,
                hotkey,
                requirements.timeline,
                public_key,
                docker_requirement.model_dump(),
            )
            if result["status"] is False:
                bt.logging.error(f"API: Allocation {hotkey} Failed : {result['msg']}")
                return JSONResponse(
                    status_code=status.HTTP_404_NOT_FOUND,
                    content={
                        "success": False,
                        "message": "Fail to allocate resource",
                        "err_detail": result["msg"],
                    },
                )
            # Iterate through the miner specs details to get gpu_name
            specs_details = await run_in_threadpool(get_miner_details, db)
            info = await process_result(
                hotkey,
                result,
                specs_details,
                private_key,
                public_key,
                docker_requirement,
                uuid_key,
            )
            result_hotkey = result["hotkey"]
            await asyncio.sleep(1)
            allocated = Allocation()
            allocated.resource = info["resource"]
            allocated.hotkey = result_hotkey
            allocated.ssh_key = info["ssh_key"]
            # allocated.regkey = info["regkey"]
            allocated.ssh_ip = info["ip"]
            allocated.ssh_port = info["port"]
            allocated.external_port = info["fixed_external_user_port"]
            allocated.ssh_username = info["username"]
            allocated.ssh_password = info["password"]
            allocated.uuid_key = info["uuid"]
            allocated.ssh_command = (
                f"ssh {info['username']}@{result['ip']} -p {str(info['port'])}"
            )
            allocated.miner_version = result.get("miner_version", 0)
            update_allocation_db(result_hotkey, info, True)
            await _update_allocation_wandb(db, wandb)
            bt.logging.info(
                f"API: Resource {allocated.hotkey} was successfully allocated"
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "success": True,
                    "message": "Resource was successfully allocated",
                    "data": jsonable_encoder(allocated),
                },
            )
        else:
            bt.logging.error("API: Invalid allocation request")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "success": False,
                    "message": "Invalid allocation request",
                    "err_detail": "Invalid hotkey, please check the hotkey",
                },
            )
    except Exception as e:
        bt.logging.error(f"API: An error occurred during allocation {e.__repr__()}")
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "success": False,
                "message": "An error occurred during allocation.",
                "err_detail": e.__repr__(),
            },
        )


async def deallocate(
    db: ComputeDb,
    wandb: ComputeWandb,
    deallocation_notify_url: str,
    status_notify_url: str,
    notify_retry_table: list,
    metagraph: Metagraph,
    dendrite: Dendrite,
    hotkey: str,
    uuid_key: str,
    request: Request, # TODO: not use, remove?
    notify_flag: bool = False,
    pubsub_client: PubSubClient | None = None,
    validator_hotkey: str | None = None,
) -> JSONResponse:
    """
    The GPU deallocate API endpoint.
    hotkey: The miner hotkey to deallocate the gpu resource.
    """
    # client_host = request.client.host
    # Instantiate the connection to the db
    try:
        # Instantiate the connection to the db
        cursor = db.get_cursor()
        # Retrieve the allocation details for the given hotkey
        cursor.execute(
            "SELECT details, hotkey FROM allocation WHERE hotkey = ?",
            (hotkey,),
        )
        row = cursor.fetchone()
        if row:
            # Parse the JSON string in the 'details' column
            info = json.loads(row[0])
            result_hotkey = row[1]
            regkey = info["regkey"]
            uuid_key_db = info["uuid"]
            if uuid_key_db == uuid_key:
                if hotkey in metagraph.hotkeys:
                    retry_count = 0
                    while retry_count < MAX_NOTIFY_RETRY:
                        if await get_deregister_response_status(
                            metagraph, hotkey, regkey, dendrite
                        ):
                            bt.logging.info(
                                f"API: Resource {hotkey} deallocated successfully"
                            )
                            break
                        else:
                            retry_count += 1
                            bt.logging.info(
                                f"API: Resource {hotkey} no response to deallocated signal - retry {retry_count}"
                            )
                            await asyncio.sleep(1)
                    if retry_count == MAX_NOTIFY_RETRY:
                        bt.logging.error(
                            f"API: Resource {hotkey} deallocated successfully without response."
                        )
                deallocated_at = datetime.now(timezone.utc)
                update_allocation_db(result_hotkey, info, False)
                await _update_allocation_wandb(db, wandb)
                # Notify the deallocation event when the client is localhost
                if notify_flag:
                    response = await _notify_allocation_status(
                        deallocation_notify_url,
                        status_notify_url,
                        event_time=deallocated_at,
                        hotkey=hotkey,
                        uuid=uuid_key,
                        event="DEALLOCATION",
                        details="deallocate trigger via API interface",
                        pubsub_client=pubsub_client,
                        validator_hotkey=validator_hotkey,
                    )
                    if response:
                        bt.logging.info(
                            f"API: Notify deallocation event is success on {hotkey} "
                        )
                    else:
                        bt.logging.info(
                            f"API: Notify deallocation event is failed on {hotkey} "
                        )
                        notify_retry_table.append(
                            {
                                "deallocated_at": deallocated_at,
                                "hotkey": hotkey,
                                "uuid": uuid_key,
                                "event": "DEALLOCATION",
                                "details": "deallocate trigger via API interface",
                            }
                        )
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content={
                        "success": True,
                        "message": "Resource deallocated successfully.",
                    },
                )
            else:
                bt.logging.error(f"API: Invalid UUID key for {hotkey}")
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={
                        "success": False,
                        "message": "Deallocation not successfully, please try again.",
                        "err_detail": "Invalid UUID key",
                    },
                )
        else:
            bt.logging.info("API: No allocation details found for the provided hotkey")
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No allocation details found for the provided hotkey.",
                    "err_detail": "No allocation details found for the provided hotkey.",
                },
            )
    except Exception as e:
        bt.logging.error(f"API: An error occurred during deallocation {e.__repr__()}")
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "success": False,
                "message": "An error occurred during deallocation.",
                "err_detail": e.__repr__(),
            },
        )
    finally:
        cursor.close()


async def check_miner_status(
    metagraph: Metagraph, dendrite: Dendrite, hotkey_list: list[str], query_version: bool = False
) -> JSONResponse:
    checking_list = []
    for hotkey in hotkey_list:
        checking_result = {"hotkey": hotkey, "status": "Not Found"}
        for axon in metagraph.axons:
            if axon.hotkey == hotkey:
                try:
                    if query_version:
                        checking_result = {"hotkey": hotkey, "version": axon.version}
                    else:
                        register_response = await dendrite(
                            axon,
                            Allocate(
                                timeline=1,
                                checking=True,
                            ),
                            timeout=60,
                        )
                        if register_response:
                            if register_response["status"] is True:
                                checking_result = {
                                    "hotkey": hotkey,
                                    "status": "Docker OFFLINE",
                                }
                            else:
                                checking_result = {
                                    "hotkey": hotkey,
                                    "status": "Docker ONLINE",
                                }
                        else:
                            checking_result = {
                                "hotkey": hotkey,
                                "status": "Miner NO_RESPONSE",
                            }
                except Exception as e:
                    bt.logging.error(f"API: An error occur during the : {e}")
                    checking_result = {"hotkey": hotkey, "status": "Unknown"}
        checking_list.append(checking_result)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "success": True,
            "message": "List hotkey status successfully.",
            "data": jsonable_encoder(checking_list),
        },
    )


async def set_docker_action(
    action: str,
    db: ComputeDb,
    ssh_key: str,
    metagraph: Metagraph,
    dendrite: Dendrite,
    hotkey: str,
    uuid_key: str,
    key_type: str = "user"
) -> JSONResponse:
    # Instantiate the connection to the db
    cursor = db.get_cursor()
    try:
        # Retrieve the allocation details for the given hotkey
        cursor.execute(
            "SELECT details, hotkey FROM allocation WHERE hotkey = ?",
            (hotkey,),
        )
        row = cursor.fetchone()
        if row:
            # Parse the JSON string in the 'details' column
            info = json.loads(row[0])
            # result_hotkey = row[1]
            # username = info["username"]
            # password = info["password"]
            # port = info["port"]
            # ip = info["ip"]
            regkey = info["regkey"]
            uuid_key_db = info["uuid"]
            docker_action = {
                "action": action,
                "ssh_key": ssh_key,
                "key_type": key_type
            }
            if uuid_key_db == uuid_key:
                index = metagraph.hotkeys.index(hotkey)
                axon = metagraph.axons[index]
                # run_start = time.time()
                allocate_class = Allocate(
                    timeline=1,
                    device_requirement={},
                    checking=False,
                    public_key=regkey,
                    docker_change=True,
                    docker_action=docker_action,
                )
                response = await dendrite(axon, allocate_class, timeout=10)
                # run_end = time.time()
                # time_eval = run_end - run_start
                # bt.logging.info(f"API: Stop docker container in: {run_end - run_start:.2f} seconds")
                if response and response["status"] is True:
                    bt.logging.info(f"API: Resource {hotkey} docker {action} successfully")
                    return JSONResponse(
                        status_code=status.HTTP_200_OK,
                        content={
                            "success": True,
                            "message": f"Resource {action} successfully.",
                        },
                    )
                else:
                    bt.logging.error(f"API: Resource {hotkey} docker {action} without response.")
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content={
                            "success": False,
                            "message": f"{action} not successfully, please try again.",
                        },
                    )

            else:
                bt.logging.error(f"API: Invalid UUID key for {hotkey}")
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={
                        "success": False,
                        "message": f"{action} not successfully, please try again.",
                        "err_detail": "Invalid UUID key",
                    },
                )
        else:
            bt.logging.info("API: No allocation details found for the provided hotkey")
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "success": False,
                    "message": "No allocation details found for the provided hotkey.",
                    "err_detail": "No allocation details found for the provided hotkey.",
                },
            )
    except Exception as e:
        bt.logging.error(
            f"API: An error occurred during {action} operation {e.__repr__()}"
        )
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "success": False,
                "message": f"An error occurred during {action} operation.",
                "err_detail": e.__repr__(),
            },
        )
    finally:
        cursor.close()


async def _allocate_container_hotkey(
    metagraph: Metagraph,
    dendrite: Dendrite,
    requirements: DeviceRequirement,
    hotkey: str,
    timeline: int,
    public_key: str,
    docker_requirement: DockerRequirement,
) -> dict:
    """
    Allocate the container with the given hotkey.
    Generate ssh connection for given device requirements and timeline.
    """
    device_requirement = {
        "cpu": {"count": 1},
        "gpu": {
            "count": 1,
            "capacity": int(requirements.gpu_size) * 1000,
            "type": requirements.gpu_type,
        },
        "hard_disk": {"capacity": 1073741824},
        "ram": {"capacity": 1073741824},
    }
    docker_requirement["base_image"] = "pytorch/pytorch:2.5.1-cuda12.4-cudnn9-runtime"

    # Start of allocation process
    bt.logging.info(f"API: Starting container allocation with hotkey: {hotkey}")
    bt.logging.trace(f"API: Docker Requirement: {docker_requirement}")

    # Instantiate the connection to the db
    for axon in metagraph.axons:
        if axon.hotkey == hotkey:
            attempt = 0
            # Retry allocation up to max_retries times
            while attempt < MAX_ALLOCATION_RETRY:
                attempt += 1
                register_response = await check_and_allocate(
                    hotkey,
                    axon,
                    dendrite,
                    timeline,
                    device_requirement,
                    60,
                    public_key,
                    docker_requirement,
                )
                if not register_response:
                    bt.logging.warning(
                        f"API: Allocation check failed for hotkey: {hotkey}"
                    )
                    continue
                if register_response.get("status") is True:
                    return register_response
                # Log or print retry attempt (optional)
                bt.logging.trace(
                    f"API: Attempt {attempt} failed for hotkey {hotkey}, retrying..."
                ) if attempt < MAX_ALLOCATION_RETRY else None
                await asyncio.sleep(10)  # Sleep before the next retry attempt
    return {"status": False, "msg": "Requested resource is not available."}


async def _update_allocation_wandb(
    db: ComputeDb,
    wandb: ComputeWandb,
) -> None:
    """
    Update the allocated hotkeys in wandb.
    """
    hotkey_list = get_hotkey_list(db)
    try:
        # Get penalized hotkeys list to pass as required parameter
        penalized_hotkeys = await run_in_threadpool(
            wandb.get_penalized_hotkeys_checklist, [], False
        )
        await run_in_threadpool(wandb.update_allocated_hotkeys, hotkey_list, penalized_hotkeys)
    except Exception as e:
        bt.logging.info(f"API: Error updating wandb : {e}")
        return


async def _notify_allocation_status(
    deallocation_notify_url: str,
    status_notify_url: str,
    event_time: datetime,
    hotkey: str,
    uuid: str,
    event: str,
    details: str | None = "", # TODO: not use, remove?
    pubsub_client: PubSubClient | None = None,
    validator_hotkey: str | None = None,
) -> dict | None:
    """
    Notify the allocation by hotkey and status using PubSub with webhook fallback.
    """
    # Try PubSub first if client is available
    if pubsub_client and validator_hotkey:
        bt.logging.info(f"API: Attempting PubSub notification for {event} event on hotkey: {hotkey}")

        # Create message factory
        factory = MessageFactory('register-api', validator_hotkey)

        # Define webhook fallback function
        async def webhook_fallback():
            """Fallback function to send webhook notification."""
            bt.logging.info(f"API: Executing webhook fallback for {event} event on hotkey: {hotkey}")
            webhook_result = await _send_webhook_notification(
                deallocation_notify_url, status_notify_url, event_time,
                hotkey, uuid, event, details
            )

            # Add fallback indicators to distinguish from direct webhook calls
            if webhook_result and isinstance(webhook_result, dict):
                webhook_result["fallback_used"] = True
                webhook_result["fallback_reason"] = "pubsub_publish_failed"
                # Store any pubsub message ID if available (though it won't be in this case)
                webhook_result["pubsub_message_id"] = None

            return webhook_result

        try:
            result = None
            # Create appropriate PubSub message based on event type
            if event == "DEALLOCATION":
                message = create_allocation_ended_message(
                    factory=factory,
                    miner_hotkey=hotkey,
                    allocation_uuid=uuid,
                    deallocation_reason=details or "API triggered deallocation",
                    correlation_id=f"dealloc_{uuid}_{int(time.time())}"
                )
                # Use allocation-events topic for deallocation with webhook fallback
                result = await pubsub_client.publish_to_allocation_events(
                    message,
                    fallback_callback=webhook_fallback
                )

            elif event == "OFFLINE":
                message = create_miner_offline_message(
                    factory=factory,
                    miner_hotkey=hotkey,
                    reason=details or "miner_disconnected",
                    correlation_id=f"offline_{uuid}_{int(time.time())}"
                )
                # Use miner-events topic for offline status with webhook fallback
                result = await pubsub_client.publish_to_miner_events(
                    message,
                    fallback_callback=webhook_fallback
                )

            elif event == "ONLINE":
                message = create_miner_online_message(
                    factory=factory,
                    miner_hotkey=hotkey,
                    reason=details or "miner_reconnected",
                    correlation_id=f"online_{uuid}_{int(time.time())}"
                )
                # Use miner-events topic for online status with webhook fallback
                result = await pubsub_client.publish_to_miner_events(
                    message,
                    fallback_callback=webhook_fallback
                )

            # Check result and return appropriate response
            if result:
                # when result is str, its comes from pubsub publishing.
                if isinstance(result, str):
                    bt.logging.info(f"API: PubSub published for {event} event on hotkey: {hotkey}, message_id: {result}")
                    return {
                        "status": "success",
                        "method": "pubsub",
                        "message_id": result,
                        "event": event,
                        "hotkey": hotkey,
                        "uuid": uuid,
                        "acknowledged": True
                    }
                # fallback used which return dict from webhook function
                elif isinstance(result, dict) and result.get("status") == "success" and result.get("fallback_used"):
                    bt.logging.info(f"API: PubSub fallback to webhook successful for {event} event on hotkey: {hotkey}, reason: {result.get('fallback_reason')}")
                    return {
                        "status": "success",
                        "method": "webhook_fallback",
                        "fallback_reason": result.get("fallback_reason"),
                        "event": event,
                        "hotkey": hotkey,
                        "uuid": uuid,
                        "pubsub_message_id": result.get("pubsub_message_id")
                    }
                else:
                    bt.logging.warning(f"API: PubSub notification failed for {event} event on hotkey: {hotkey}, result: {result}")

        except Exception as e:
            bt.logging.error(f"API: PubSub notification error for {event} event on hotkey: {hotkey}: {e}")
            # If PubSub client fails completely, fall back to direct webhook
            pass

    # Direct webhook notification (when PubSub client not available)
    bt.logging.info(f"API: Using direct webhook notification for {event} event on hotkey: {hotkey}")
    return await _send_webhook_notification(
        deallocation_notify_url, status_notify_url, event_time,
        hotkey, uuid, event, details
    )


async def _send_webhook_notification(
    deallocation_notify_url: str,
    status_notify_url: str,
    event_time: datetime,
    hotkey: str,
    uuid: str,
    event: str,
    details: str | None = "",
) -> dict| None:
    """
    Send webhook notification for allocation events.

    Args:
        deallocation_notify_url: URL for deallocation notifications
        status_notify_url: URL for status change notifications
        event_time: Time of the event
        hotkey: Miner hotkey
        uuid: Allocation UUID
        event: Event type (DEALLOCATION, OFFLINE, ONLINE)
        details: Additional event details

    Returns:
        Dict with notification result or None if failed
    """
    bt.logging.info(f"API: Sending webhook notification for {event} event on hotkey: {hotkey}")

    headers = {
        "accept": "*/*",
        "Content-Type": "application/json",
    }
    if event == "DEALLOCATION":
        msg = {
            "time": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "deallocated_at": event_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "hotkey": hotkey,
            "status": event,
            "uuid": uuid,
        }
        notify_url = deallocation_notify_url
    elif event == "OFFLINE" or event == "ONLINE":
        msg = {
            "time": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "status_change_at": event_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "hotkey": hotkey,
            "status": event,
            "uuid": uuid,
        }
        notify_url = status_notify_url

    retries = 0
    while retries < MAX_NOTIFY_RETRY:
        try:
            # Send the POST request
            data = json.dumps(msg, separators=(',', ':'))
            cert_path = os.getenv("CERT_LOCATION")
            signature = hmac.new(secret, data.encode(), hashlib.sha256).hexdigest()
            headers["x-webhook-signature"] = signature
            response = await run_in_threadpool(
                requests.post,
                notify_url,
                headers=headers,
                data=data,
                timeout=3,
                json=True,
                verify=False,
                cert=(cert_path + "server.cer", cert_path + "server.key"),
            )
            # Check for the expected ACK in the response
            if response.status_code == 200 or response.status_code == 201:
                response_data = response.json()
                bt.logging.info(f"API: Webhook notification successful for {event} event on hotkey: {hotkey}")
                return {
                    "status": "success",
                    "method": "webhook",
                    "response_data": response_data,
                    "event": event,
                    "hotkey": hotkey,
                    "uuid": uuid
                }
            else:
                bt.logging.info(
                    f"API: Webhook notification failed for {event} event on hotkey: {hotkey}, status code: "
                    f"{response.status_code}, response: {response.text}"
                )
                return None
        except requests.exceptions.RequestException as e:
            bt.logging.info(f"API: Webhook notification error for {event} event on hotkey: {hotkey}: {e}")
        # Increment the retry counter and wait before retrying
        retries += 1
        await asyncio.sleep(NOTIFY_RETRY_PERIOD)

    bt.logging.error(f"API: Webhook notification failed for {event} event on hotkey: {hotkey} after {MAX_NOTIFY_RETRY} retries")
    return None
